import { useState, useEffect } from 'react';
import { loadGoogleMapsAPI } from '@/lib/googleMaps';

interface UseGoogleMapsReturn {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
}

export function useGoogleMaps(): UseGoogleMapsReturn {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if already loaded with all required APIs
    if (window.google &&
        window.google.maps &&
        window.google.maps.Map &&
        window.google.maps.StreetViewPanorama &&
        window.google.maps.marker &&
        window.google.maps.marker.AdvancedMarkerElement) {
      setIsLoaded(true);
      return;
    }

    const loadAPI = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        if (!apiKey) {
          throw new Error('Google Maps API key not found');
        }

        await loadGoogleMapsAPI(apiKey);

        // Wait a bit more to ensure all APIs are loaded
        await new Promise(resolve => setTimeout(resolve, 100));

        // Verify all required APIs are available
        if (!window.google?.maps?.Map ||
            !window.google?.maps?.StreetViewPanorama ||
            !window.google?.maps?.marker?.AdvancedMarkerElement) {
          throw new Error('Google Maps APIs not fully loaded');
        }

        setIsLoaded(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load Google Maps');
      } finally {
        setIsLoading(false);
      }
    };

    loadAPI();
  }, []);

  return { isLoaded, isLoading, error };
}
