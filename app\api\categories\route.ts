import { NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongo';
import Category from '@/lib/models/Category';
import { DEFAULT_CATEGORIES } from '@/utils/constants';

export async function GET() {
  try {
    await connectToDatabase();
    
    // Get categories from database
    let categories = await Category.find({ isActive: true }).lean();
    
    // If no categories in database, use default categories
    if (categories.length === 0) {
      categories = DEFAULT_CATEGORIES.map(cat => ({
        ...cat,
        _id: cat._id,
      }));
    }
    
    return NextResponse.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch categories',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    await connectToDatabase();
    
    // Initialize default categories in database
    const existingCategories = await Category.find();
    
    if (existingCategories.length === 0) {
      const categoriesToInsert = DEFAULT_CATEGORIES.map(cat => ({
        _id: cat._id,
        name: cat.name,
        description: cat.description,
        queryConfig: cat.queryConfig,
        isActive: cat.isActive,
      }));
      
      await Category.insertMany(categoriesToInsert);
      
      return NextResponse.json({
        success: true,
        message: 'Default categories initialized',
        data: categoriesToInsert
      });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Categories already exist',
      data: existingCategories
    });
  } catch (error) {
    console.error('Error initializing categories:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to initialize categories',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
