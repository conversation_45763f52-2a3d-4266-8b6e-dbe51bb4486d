import mongoose, { Schema, Document } from 'mongoose';
import { Category } from '@/types/game';

export interface CategoryDocument extends Category, Document {}

const CategorySchema = new Schema<CategoryDocument>({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  queryConfig: {
    type: Schema.Types.Mixed,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
});

// Create indexes
CategorySchema.index({ name: 1 }, { unique: true });
CategorySchema.index({ isActive: 1 });

export default mongoose.models.Category || mongoose.model<CategoryDocument>('Category', CategorySchema);
