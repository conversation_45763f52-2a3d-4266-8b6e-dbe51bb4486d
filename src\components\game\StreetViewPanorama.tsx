'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Location } from '@/types/game';
import { useGoogleMaps } from '@/hooks/useGoogleMaps';

interface StreetViewPanoramaProps {
  location: Location;
  onPanoramaLoad?: () => void;
  className?: string;
}

export default function StreetViewPanorama({
  location,
  onPanoramaLoad,
  className = ''
}: StreetViewPanoramaProps) {
  const panoramaRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const panoramaInstance = useRef<google.maps.StreetViewPanorama | null>(null);
  const { isLoaded: mapsLoaded, isLoading: mapsLoading, error: mapsError } = useGoogleMaps();

  useEffect(() => {
    if (mapsLoaded) {
      initializePanorama();
    }
  }, [location, mapsLoaded]);

  useEffect(() => {
    if (mapsError) {
      setError(mapsError);
      setIsLoading(false);
    }
  }, [mapsError]);

  const initializePanorama = async () => {
    if (!panoramaRef.current || !mapsLoaded) return;

    setIsLoading(true);
    setError(null);

    try {
      // Verify Google Maps APIs are available
      if (!window.google?.maps?.StreetViewPanorama) {
        throw new Error('Google Maps StreetViewPanorama not available');
      }

      // Create Street View panorama
      const panorama = new google.maps.StreetViewPanorama(panoramaRef.current, {
        position: location,
        pov: {
          heading: 0,
          pitch: 0,
        },
        zoom: 1,
        addressControl: false,
        linksControl: true,
        panControl: true,
        enableCloseButton: false,
        showRoadLabels: false,
        motionTracking: false,
        motionTrackingControl: false,
      });

      panoramaInstance.current = panorama;

      // Add event listeners
      panorama.addListener('status_changed', () => {
        const status = panorama.getStatus();
        if (status === google.maps.StreetViewStatus.OK) {
          setIsLoading(false);
          onPanoramaLoad?.();
        } else {
          setError('Street View not available for this location');
          setIsLoading(false);
        }
      });

      panorama.addListener('error', () => {
        setError('Failed to load Street View');
        setIsLoading(false);
      });

    } catch (err) {
      console.error('Error initializing Street View:', err);
      setError(err instanceof Error ? err.message : 'Failed to load Street View');
      setIsLoading(false);
    }
  };

  const resetView = () => {
    if (panoramaInstance.current) {
      panoramaInstance.current.setPov({
        heading: 0,
        pitch: 0,
      });
      panoramaInstance.current.setZoom(1);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading Street View...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <p className="text-red-600 mb-2">{error}</p>
            <button 
              onClick={initializePanorama}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Street View Container */}
      <div 
        ref={panoramaRef} 
        className="w-full h-full min-h-[400px] rounded-lg overflow-hidden"
      />

      {/* Controls */}
      {!isLoading && !error && (
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={resetView}
            className="px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow hover:bg-opacity-100 transition-all text-sm"
            title="Reset View"
          >
            🔄 Reset
          </button>
        </div>
      )}
    </div>
  );
}
