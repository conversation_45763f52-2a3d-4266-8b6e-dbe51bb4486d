import { Location } from '@/types/game';

// Extend the Window interface to include google
declare global {
  interface Window {
    google: typeof google;
  }
}

// Extend Google Maps types for AdvancedMarkerElement
declare global {
  namespace google.maps.marker {
    interface AdvancedMarkerElementDragEvent {
      position: google.maps.LatLng;
    }
  }
}

// Global flag to track if API is loading
let isLoading = false;
let loadPromise: Promise<void> | null = null;

/**
 * Load Google Maps JavaScript API with proper async loading
 * @param apiKey Google Maps API key
 * @returns Promise that resolves when the API is loaded
 */
export function loadGoogleMapsAPI(apiKey: string): Promise<void> {
  // If already loaded, resolve immediately
  if (window.google && window.google.maps) {
    return Promise.resolve();
  }

  // If already loading, return the existing promise
  if (isLoading && loadPromise) {
    return loadPromise;
  }

  // Check if script already exists
  const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
  if (existingScript) {
    return new Promise((resolve) => {
      const checkLoaded = () => {
        if (window.google && window.google.maps) {
          resolve();
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
    });
  }

  isLoading = true;
  loadPromise = new Promise((resolve, reject) => {
    // Create script element with proper async loading
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry,marker&loading=async`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      isLoading = false;
      resolve();
    };

    script.onerror = () => {
      isLoading = false;
      loadPromise = null;
      reject(new Error('Failed to load Google Maps API'));
    };

    document.head.appendChild(script);
  });

  return loadPromise;
}

/**
 * Generate a random location for Street View (placeholder implementation)
 * In a real implementation, this would use a curated list or Google's API
 * @returns Random location
 */
export function generateRandomStreetViewLocation(): Location {
  // This is a simplified implementation
  // In production, you'd want to use a curated list of known Street View locations
  const locations: Location[] = [
    { lat: 40.7128, lng: -74.0060 }, // New York
    { lat: 51.5074, lng: -0.1278 },  // London
    { lat: 48.8566, lng: 2.3522 },   // Paris
    { lat: 35.6762, lng: 139.6503 }, // Tokyo
    { lat: -33.8688, lng: 151.2093 }, // Sydney
    { lat: 37.7749, lng: -122.4194 }, // San Francisco
    { lat: 55.7558, lng: 37.6176 },  // Moscow
    { lat: -22.9068, lng: -43.1729 }, // Rio de Janeiro
  ];

  return locations[Math.floor(Math.random() * locations.length)];
}

/**
 * Check if Street View is available at a location
 * @param location Location to check
 * @returns Promise that resolves to boolean
 */
export function checkStreetViewAvailability(location: Location): Promise<boolean> {
  return new Promise((resolve) => {
    if (!window.google || !window.google.maps) {
      resolve(false);
      return;
    }

    const streetViewService = new google.maps.StreetViewService();
    
    streetViewService.getPanorama({
      location: location,
      radius: 50,
    }, (data: google.maps.StreetViewPanoramaData | null, status: google.maps.StreetViewStatus) => {
      resolve(status === google.maps.StreetViewStatus.OK);
    });
  });
}

/**
 * Calculate distance using Google Maps Geometry library
 * @param point1 First location
 * @param point2 Second location
 * @returns Distance in kilometers
 */
export function calculateDistanceWithGoogleMaps(point1: Location, point2: Location): number {
  if (!window.google || !window.google.maps || !window.google.maps.geometry) {
    throw new Error('Google Maps API not loaded');
  }

  const latLng1 = new google.maps.LatLng(point1.lat, point1.lng);
  const latLng2 = new google.maps.LatLng(point2.lat, point2.lng);

  const distance = google.maps.geometry.spherical.computeDistanceBetween(latLng1, latLng2);
  return Math.round((distance / 1000) * 100) / 100; // Convert to km and round to 2 decimal places
}
