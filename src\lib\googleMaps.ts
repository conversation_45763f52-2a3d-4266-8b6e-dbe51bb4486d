import { Location } from '@/types/game';

/**
 * Load Google Maps JavaScript API
 * @param apiKey Google Maps API key
 * @returns Promise that resolves when the API is loaded
 */
export function loadGoogleMapsAPI(apiKey: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (window.google && window.google.maps) {
      resolve();
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry`;
    script.async = true;
    script.defer = true;

    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load Google Maps API'));

    document.head.appendChild(script);
  });
}

/**
 * Generate a random location for Street View (placeholder implementation)
 * In a real implementation, this would use a curated list or Google's API
 * @returns Random location
 */
export function generateRandomStreetViewLocation(): Location {
  // This is a simplified implementation
  // In production, you'd want to use a curated list of known Street View locations
  const locations: Location[] = [
    { lat: 40.7128, lng: -74.0060 }, // New York
    { lat: 51.5074, lng: -0.1278 },  // London
    { lat: 48.8566, lng: 2.3522 },   // Paris
    { lat: 35.6762, lng: 139.6503 }, // Tokyo
    { lat: -33.8688, lng: 151.2093 }, // Sydney
    { lat: 37.7749, lng: -122.4194 }, // San Francisco
    { lat: 55.7558, lng: 37.6176 },  // Moscow
    { lat: -22.9068, lng: -43.1729 }, // Rio de Janeiro
  ];

  return locations[Math.floor(Math.random() * locations.length)];
}

/**
 * Check if Street View is available at a location
 * @param location Location to check
 * @returns Promise that resolves to boolean
 */
export function checkStreetViewAvailability(location: Location): Promise<boolean> {
  return new Promise((resolve) => {
    if (!window.google || !window.google.maps) {
      resolve(false);
      return;
    }

    const streetViewService = new google.maps.StreetViewService();
    
    streetViewService.getPanorama({
      location: location,
      radius: 50,
    }, (data, status) => {
      resolve(status === google.maps.StreetViewStatus.OK);
    });
  });
}

/**
 * Calculate distance using Google Maps Geometry library
 * @param point1 First location
 * @param point2 Second location
 * @returns Distance in kilometers
 */
export function calculateDistanceWithGoogleMaps(point1: Location, point2: Location): number {
  if (!window.google || !window.google.maps || !window.google.maps.geometry) {
    throw new Error('Google Maps API not loaded');
  }

  const latLng1 = new google.maps.LatLng(point1.lat, point1.lng);
  const latLng2 = new google.maps.LatLng(point2.lat, point2.lng);

  const distance = google.maps.geometry.spherical.computeDistanceBetween(latLng1, latLng2);
  return Math.round((distance / 1000) * 100) / 100; // Convert to km and round to 2 decimal places
}
